FROM xianmu-registry-registry.cn-hangzhou.cr.aliyuncs.com/base/pyodps-pandas:3.8-slim-ffmpeg-v2 AS custom-python-base

# WORKDIR /app/spider

# COPY ./requirements.txt ./requirements.txt

# RUN pip install --index-url=https://mirrors.aliyun.com/pypi/simple/ --trusted-host=mirrors.aliyun.com --default-timeout=30 -r requirements.txt

# FROM custom-python-base

WORKDIR /app/spider

RUN mkdir -p /app/spider/scripts

ENV ALIBABA_CLOUD_ACCESS_KEY_ID=""
ENV ALIBABA_CLOUD_ACCESS_KEY_SECRET=""

# COPY ./run_all.sh ./run_all.sh
COPY ./run_all_docker.sh ./run_all.sh
COPY ./send_feishu_notification.py ./send_feishu_notification.py
COPY ./scripts/ /app/spider/scripts
# COPY ./qiyu_call_analytics/七鱼话务语音分析.py /app/spider/scripts/qiyu_call_audio_recognize.py

ENV FEISHU_NOTIFY_TOKEN="4c945cb4-1ab8-450d-bb78-89db0f578cba"
ENV FEISHU_XGPT_APP_SECRET="uQolgem8B8fwuTlsER0bZdUe7xZjueHU"
ENV MAX_JOBS=5
ENV SUSPICIOUS_THRESHOLD=300
ENV FILES_TO_SKIP="biaoguo_with_prop_spider.py"

RUN unset http_proxy HTTP_PROXY https_proxy HTTPS_PROXY && ls ./scripts && chmod +x /app/spider/run_all.sh

ENTRYPOINT ["/bin/bash", "/app/spider/run_all.sh"]